using Microsoft.AspNetCore.Mvc;
using NafaPlace.Identity.API.Services;

namespace NafaPlace.Identity.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UserRoleFixController : ControllerBase
{
    private readonly UserRoleFixService _userRoleFixService;
    private readonly ILogger<UserRoleFixController> _logger;

    public UserRoleFixController(UserRoleFixService userRoleFixService, ILogger<UserRoleFixController> logger)
    {
        _userRoleFixService = userRoleFixService;
        _logger = logger;
    }

    [HttpPost("assign-client-role")]
    public async Task<IActionResult> AssignClientRoleToUsersWithoutRoles()
    {
        try
        {
            await _userRoleFixService.AssignClientRoleToUsersWithoutRolesAsync();
            return Ok(new { message = "Rôle Client assigné avec succès aux utilisateurs sans rôle." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'assignation du rôle Client.");
            return StatusCode(500, new { message = "Erreur lors de l'assignation du rôle Client.", error = ex.Message });
        }
    }

    [HttpGet("users-with-roles")]
    public async Task<IActionResult> GetUsersWithRoles()
    {
        try
        {
            var users = await _userRoleFixService.GetUsersWithRoleInfoAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs avec rôles.");
            return StatusCode(500, new { message = "Erreur lors de la récupération des utilisateurs.", error = ex.Message });
        }
    }
}
