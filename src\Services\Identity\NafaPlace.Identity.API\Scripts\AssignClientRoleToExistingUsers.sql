-- Script pour assigner le rôle "Client" aux utilisateurs existants qui n'ont pas de rôle assigné

-- Vérifier les utilisateurs sans rôle
SELECT u."Id", u."Username", u."Email", u."CreatedAt"
FROM "Users" u
LEFT JOIN "UserRoles" ur ON u."Id" = ur."UserId"
WHERE ur."UserId" IS NULL;

-- Assigner le rôle "Client" (Id = 3) aux utilisateurs sans rôle
INSERT INTO "UserRoles" ("UserId", "RoleId", "CreatedAt")
SELECT u."Id", 3, CURRENT_TIMESTAMP
FROM "Users" u
LEFT JOIN "UserRoles" ur ON u."Id" = ur."UserId"
WHERE ur."UserId" IS NULL;

-- Vérifier le résultat
SELECT u."Id", u."Username", u."Email", r."Name" as "Role"
FROM "Users" u
JOIN "UserRoles" ur ON u."Id" = ur."UserId"
JOIN "Roles" r ON ur."RoleId" = r."Id"
ORDER BY u."Id";
