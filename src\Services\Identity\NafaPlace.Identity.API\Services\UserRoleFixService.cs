using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Domain.Models;

namespace NafaPlace.Identity.API.Services;

public class UserRoleFixService
{
    private readonly IdentityDbContext _context;
    private readonly ILogger<UserRoleFixService> _logger;

    public UserRoleFixService(IdentityDbContext context, ILogger<UserRoleFixService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task AssignClientRoleToUsersWithoutRolesAsync()
    {
        try
        {
            _logger.LogInformation("Début de l'assignation du rôle Client aux utilisateurs sans rôle...");

            // Trouver les utilisateurs sans rôle
            var usersWithoutRoles = await _context.Users
                .Where(u => !u.UserRoles.Any())
                .ToListAsync();

            if (!usersWithoutRoles.Any())
            {
                _logger.LogInformation("Aucun utilisateur sans rôle trouvé.");
                return;
            }

            _logger.LogInformation($"Trouvé {usersWithoutRoles.Count} utilisateur(s) sans rôle.");

            // Obtenir le rôle Client
            var clientRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Client");
            if (clientRole == null)
            {
                _logger.LogError("Le rôle 'Client' n'existe pas dans la base de données.");
                return;
            }

            // Assigner le rôle Client à chaque utilisateur sans rôle
            var userRolesToAdd = new List<UserRole>();
            foreach (var user in usersWithoutRoles)
            {
                _logger.LogInformation($"Assignation du rôle Client à l'utilisateur: {user.Username} ({user.Email})");
                
                userRolesToAdd.Add(new UserRole
                {
                    UserId = user.Id,
                    RoleId = clientRole.Id,
                    CreatedAt = DateTime.UtcNow
                });
            }

            // Ajouter tous les rôles en une seule fois
            await _context.UserRoles.AddRangeAsync(userRolesToAdd);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Rôle Client assigné avec succès à {userRolesToAdd.Count} utilisateur(s).");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'assignation du rôle Client aux utilisateurs.");
            throw;
        }
    }

    public async Task<List<UserWithRoleInfo>> GetUsersWithRoleInfoAsync()
    {
        try
        {
            var usersWithRoles = await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .Select(u => new UserWithRoleInfo
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    CreatedAt = u.CreatedAt,
                    Roles = u.UserRoles.Select(ur => ur.Role.Name).ToList()
                })
                .ToListAsync();

            return usersWithRoles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des informations utilisateurs avec rôles.");
            throw;
        }
    }
}

public class UserWithRoleInfo
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public List<string> Roles { get; set; } = new();
}
