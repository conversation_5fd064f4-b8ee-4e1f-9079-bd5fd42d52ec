@page "/account/home"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject NavigationManager NavigationManager
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage
@inject IAuthService AuthService
@inject AuthenticationStateProvider AuthStateProvider

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <AuthorizeView>
                        <Authorized>
                            <h2 class="mb-4">Bienvenue sur NafaPlace!</h2>
                            <p class="lead">Vous êtes connecté en tant que @context.User.Identity?.Name</p>
                            
                            <div class="mt-4">
                                <button class="btn btn-danger" @onclick="Logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                </button>
                            </div>
                        </Authorized>
                        <NotAuthorized>
                            <div class="text-center py-5">
                                <h3 class="text-danger">Accès non autorisé</h3>
                                <p class="lead">Vous devez être connecté pour accéder à cette page.</p>
                                <a href="/auth/login" class="btn btn-primary mt-3">Se connecter</a>
                            </div>
                        </NotAuthorized>
                    </AuthorizeView>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        
        if (user.Identity == null || !user.Identity.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/auth/login");
        }
    }

    private async Task Logout()
    {
        await AuthService.Logout();
        NavigationManager.NavigateTo("/", true);
    }
}
