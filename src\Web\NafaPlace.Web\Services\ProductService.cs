using System.Net.Http.Json;
using NafaPlace.Web.Models.Catalog;
using NafaPlace.Web.Models.Common;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;

namespace NafaPlace.Web.Services;

public class ProductService : IProductService
{
    private readonly HttpClient _httpClient;

    public ProductService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<ProductSearchResponse> SearchProductsAsync(ProductSearchRequest request)
    {
        try
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            }

            if (request.CategoryIds != null && request.CategoryIds.Any())
            {
                foreach (var categoryId in request.CategoryIds)
                {
                    queryParams.Add($"categoryIds={categoryId}");
                }
            }

            queryParams.Add($"page={request.Page}");
            queryParams.Add($"pageSize={request.PageSize}");

            if (request.MinPrice.HasValue)
            {
                queryParams.Add($"minPrice={request.MinPrice.Value}");
            }

            if (request.MaxPrice.HasValue)
            {
                queryParams.Add($"maxPrice={request.MaxPrice.Value}");
            }

            if (request.InStockOnly)
            {
                queryParams.Add($"inStockOnly={request.InStockOnly}");
            }

            if (!string.IsNullOrEmpty(request.SortBy))
            {
                queryParams.Add($"sortBy={Uri.EscapeDataString(request.SortBy)}");
            }

            queryParams.Add($"sortDescending={request.SortDescending}");

            // Utiliser l'API existante /api/v1/products au lieu de /search
            var url = $"api/v1/products?{string.Join("&", queryParams)}";
            Console.WriteLine($"Recherche de produits avec l'URL: {url}");
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>(url);

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Produits trouvés avec détails complets: {apiResponse.Items.Count()}");

                // Convertir la réponse de l'API vers le format attendu par le site web
                var response = new ProductSearchResponse
                {
                    Products = apiResponse.Items,
                    TotalItems = apiResponse.TotalCount,
                    Page = request.Page,
                    PageSize = request.PageSize
                };

                Console.WriteLine($"Produits trouvés: {response.Products.Count()}");
                return response;
            }

            return new ProductSearchResponse
            {
                Products = new List<ProductDto>(),
                TotalItems = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la recherche de produits: {ex.Message}");
            return new ProductSearchResponse 
            { 
                Products = new List<ProductDto>(),
                TotalItems = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }
    }

    public async Task<ProductDto?> GetProductByIdAsync(int id)
    {
        try
        {
            Console.WriteLine($"Récupération du produit {id}...");
            var product = await _httpClient.GetFromJsonAsync<ProductDto>($"api/v1/products/{id}");
            Console.WriteLine($"Produit {id} récupéré avec succès");
            return product;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du produit {id}: {ex.Message}");
            return null;
        }
    }

    public async Task<IEnumerable<ProductDto>> GetRelatedProductsAsync(int productId, int count = 4)
    {
        try
        {
            Console.WriteLine($"Récupération des produits liés à {productId}...");

            // Récupérer d'abord le produit pour connaître sa catégorie
            var product = await GetProductByIdAsync(productId);
            if (product?.Category?.Id != null)
            {
                // Récupérer des produits de la même catégorie
                var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>($"api/v1/products?page=1&pageSize={count + 1}");

                if (apiResponse?.Items != null)
                {
                    // Filtrer pour exclure le produit actuel et limiter le nombre
                    var relatedProducts = apiResponse.Items
                        .Where(p => p.Id != productId)
                        .Take(count)
                        .ToList();

                    Console.WriteLine($"Produits liés récupérés: {relatedProducts.Count}");
                    return relatedProducts;
                }
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits liés: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetFeaturedProductsAsync(int count = 8)
    {
        try
        {
            Console.WriteLine($"Récupération des produits en vedette...");
            var productsResponse = await _httpClient.GetFromJsonAsync<IEnumerable<ProductDto>>($"api/v1/products/featured?count={count}");
            
            if (productsResponse != null)
            {
                Console.WriteLine($"Produits en vedette récupérés: {productsResponse.Count()}");
                return productsResponse;
            }
            
            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits en vedette: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetNewProductsAsync(int count = 8)
    {
        try
        {
            Console.WriteLine($"Récupération des nouveaux produits...");
            // Utiliser l'endpoint spécifique pour les nouveaux produits
            var productsResponse = await _httpClient.GetFromJsonAsync<IEnumerable<ProductDto>>($"api/v1/products/new?count={count}");

            if (productsResponse != null)
            {
                Console.WriteLine($"Nouveaux produits récupérés: {productsResponse.Count()}");
                return productsResponse;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des nouveaux produits: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetSellerProductsAsync(int sellerId, int page, int pageSize)
    {
        try
        {
            Console.WriteLine($"Récupération des produits du vendeur {sellerId}...");
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>(
                $"api/v1/products?sellerId={sellerId}&page={page}&pageSize={pageSize}");
            Console.WriteLine($"Produits du vendeur récupérés: {apiResponse?.Items?.Count() ?? 0}");

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Produits du vendeur récupérés avec détails complets: {apiResponse.Items.Count()}");
                return apiResponse.Items;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits du vendeur: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId, int page, int pageSize)
    {
        try
        {
            Console.WriteLine($"Récupération des produits de la catégorie {categoryId}...");
            var productsResponse = await _httpClient.GetFromJsonAsync<IEnumerable<ProductDto>>($"api/v1/products/category/{categoryId}?page={page}&pageSize={pageSize}");
            Console.WriteLine($"Produits de la catégorie récupérés: {productsResponse?.Count() ?? 0}");
            
            if (productsResponse != null)
            {
                Console.WriteLine($"Produits de la catégorie récupérés avec détails complets: {productsResponse.Count()}");
                return productsResponse;
            }
            
            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits de la catégorie: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public async Task<IEnumerable<ProductDto>> GetAllProductsAsync()
    {
        try
        {
            Console.WriteLine("Récupération de tous les produits...");
            var apiResponse = await _httpClient.GetFromJsonAsync<PagedResultDto<ProductDto>>("api/v1/products?page=1&pageSize=1000");
            Console.WriteLine($"Produits récupérés: {apiResponse?.Items?.Count() ?? 0}");

            if (apiResponse != null && apiResponse.Items != null)
            {
                Console.WriteLine($"Produits récupérés avec détails complets: {apiResponse.Items.Count()}");
                return apiResponse.Items;
            }

            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de tous les produits: {ex.Message}");
            return new List<ProductDto>();
        }
    }

    public string GetImageUrl(ProductImageDto image, bool thumbnail = false)
    {
        if (image == null)
        {
            return "/images/no-image.png";
        }

        var url = thumbnail ? image.ThumbnailUrl : image.Url;

        if (string.IsNullOrEmpty(url))
        {
            // Fallback to the other url if one is empty
            url = thumbnail ? image.Url : image.ThumbnailUrl;
        }

        if (string.IsNullOrEmpty(url))
        {
            return "/images/no-image.png";
        }

        if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
        {
            return url;
        }

        return url.StartsWith("/") ? url : $"/{url}";
    }
}
